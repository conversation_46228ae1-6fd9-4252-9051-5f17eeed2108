<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <title>Vehicle Report</title>
    <style>
        body,
        body * {
            font-family: 'Helvetica', Arial, sans-serif !important;
        }

        .container {
            max-width: 1000px;
            margin: 20px auto;
            padding: 30px;
            background: #ffffff;
        }

        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-bottom: 3px solid #F54619;
            margin-bottom: 30px;
            padding-bottom: 20px;
            position: relative;
        }

        .header::after {
            content: '';
            position: absolute;
            bottom: -6px;
            left: 0;
            width: 100%;
        }

        .header .company-info {
            text-align: right;
            font-size: 11px;
            color: #4A5568;
            line-height: 1.8;
        }

        .report-title {
            text-align: center;
            margin: 40px 0;
            position: relative;
        }

        .report-title h2 {
            font-family: 'Helvetica', Arial, sans-serif !important;
            font-size: 20px;
            color: #2D3748;
            margin: 0;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .report-title::after {
            content: '';
            display: block;
            width: 60px;
            height: 3px;
            background: #F54619;
            margin: 15px auto;
        }

        .summary-box {
            background: #FFFFFF;
            border-radius: 12px;
            padding: 25px;
            margin: 30px 0;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
            border: 1px solid #E2E8F0;
        }

        .summary-box h3 {
            font-family: 'Helvetica', Arial, sans-serif !important;
            color: #2D3748;
            font-size: 18px;
            margin: 0 0 20px 0;
            padding-bottom: 15px;
            border-bottom: 2px solid #F54619;
        }

        .summary-grid {
            width: 100%;
            font-size: 0;
            /* Remove inline-block spacing */
        }

        .summary-item {
            display: inline-block;
            vertical-align: top;
            width: 42%;
            margin: 1%;
            background: #F8FAFC;
            padding: 15px;
            border-radius: 8px;
            border: 1px solid #EDF2F7;
            box-sizing: border-box;
            font-size: 12px;
            /* Reset font size */
        }

        /* For the last row if items are less than 3 */
        .summary-item:last-child:nth-child(3n-1) {
            margin-right: 34.66%;
        }

        .summary-item:last-child:nth-child(3n-2) {
            margin-right: 67.99%;
        }

        .summary-label {
            color: #F54619;
            font-weight: bold;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 5px;
        }

        .summary-value {
            font-size: 16px;
            color: #2D3748;
            font-weight: bold;
        }

        .details-section {
            margin-top: 40px;
        }

        .details-section h3 {
            font-family: 'Helvetica', Arial, sans-serif !important;
            color: #2D3748;
            font-size: 18px;
            margin-bottom: 20px;
            padding-bottom: 10px;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 20px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
        }

        th {
            font-family: 'Helvetica', Arial, sans-serif !important;
            background: #F54619;
            color: white;
            font-weight: bold;
            padding: 12px;
            text-align: left;
            font-size: 10px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        td {
            padding: 12px;
            border-bottom: 1px solid #EDF2F7;
            color: #4A5568;
            font-size: 10px;
        }

        tr:nth-child(even) {
            background-color: #F8FAFC;
        }

        tr:last-child td {
            border-bottom: none;
        }

        .page-footer {
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #EDF2F7;
            text-align: center;
            font-size: 10px;
            color: #718096;
        }

        @page {
            margin: 40px;
        }

        .page-break {
            page-break-before: always;
        }

        .summary-box {
            background: #FFFFFF;
            border-radius: 8px;
            padding: 25px;
            margin: 30px 0;
            border: 1px solid #E2E8F0;
        }

        table {
            width: 100%;
            border-collapse: separate;
            border-spacing: 0;
            margin: 20px 0;
            border: 1px solid #E2E8F0;
        }

        .summary-item,
        .summary-box {
            border-radius: 4px;
        }

        /* Add these styles to your existing <style> section */
        .report-title {
            text-align: center;
            margin: 40px 0;
            padding: 20px;
            background: #F8FAFC;
            border-radius: 8px;
            border: 1px solid #E2E8F0;
        }

        .report-meta {
            display: flex;
            flex-direction: column;
            gap: 8px;
            align-items: center;
        }

        .report-type {
            font-family: 'Helvetica', Arial, sans-serif !important;
            font-size: 20px;
            font-weight: bold;
            color: #2D3748;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .vehicle-info {
            font-size: 20px;

            color: #F54619;
            font-weight: bold;
        }

        .report-date {
            font-size: 16px;
            color: #64748B;
            font-weight: bold;
            float: right;
        }

        /* Remove the existing .report-title::after since we're using a different design */
        .report-title::after {
            display: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <img src="{{ public_path('assets/images/logo.png') }}" alt="Logo" width="180">
            </div>
            <div class="company-info">
                <strong style="color: #2D3748; font-size: 14px;">ControllOne</strong><br>
                12345, xyz<br>
                84135 Rome, Italy<br>
                <span style="color: #F54619;"><EMAIL></span><br>
                +391234567890
            </div>
        </div>

        <div class="report-title">
            <div class="report-meta">
                <div class="report-type">
                    @if ($reporting_type == 'routes')
                        {{ __('translations.report') }}
                    @elseif ($reporting_type == 'alarms')
                        {{ __('translations.alarm_report') }}
                    @elseif ($reporting_type == 'fuel_consumption')
                        {{ __('translations.fuel_report') }}
                    @endif
                    @if ($reporting_type == 'routes' && isset($data['summary']['vehicle']))
                        <span class="vehicle-info">{{ $data['summary']['vehicle'] }}</span>
                    @endif
                    <span class="report-date">{{ $date ?? now()->format('d-m-Y') }}</span>
                </div>
            </div>
        </div>



        @if ($reporting_type == 'routes')
            <div class="summary-box">
                <h3>Trip Summary</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.distance')</div>
                        <div class="summary-value">{{ $data['summary']['total_distance'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.departure_time')</div>
                        <div class="summary-value">{{ $data['summary']['start_time'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.arrival_time')</div>
                        <div class="summary-value">{{ $data['summary']['end_time'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.travel_duration')</div>
                        <div class="summary-value">{{ $data['summary']['total_duration'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.fuel_consumption')</div>
                        <div class="summary-value">{{ $data['summary']['total_fuel'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.total_stops')</div>
                        <div class="summary-value">{{ $data['summary']['total_stops'] }}</div>
                    </div>
                    <div class="summary-item" style="width: 100%">
                        <div class="summary-label">@lang('translations.departure_address')</div>
                        <div class="summary-value">{{ $data['summary']['start_point_address'] }}</div>
                    </div>
                    <div class="summary-item" style="width: 100%">
                        <div class="summary-label">@lang('translations.arrival_address')</div>
                        <div class="summary-value">{{ $data['summary']['end_point_address'] }}</div>
                    </div>
                </div>
            </div>

            @if (isset($data['summary']['geofence_events']) && count($data['summary']['geofence_events']) > 0)
                <div class="summary-box">
                    <h3 style="border-bottom: 0">@lang('translations.geofence_events')</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>@lang('translations.event_type')</th>
                                <th>@lang('translations.geofence')</th>
                                <th>@lang('translations.time')</th>
                                <th>@lang('translations.location')</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($data['summary']['geofence_events'] as $event)
                                <tr>
                                    <td>
                                        <div style="display: flex; align-items: center;">
                                            <span
                                                style="
                                                display: inline-block;
                                                width: 8px;
                                                height: 8px;
                                                border-radius: 50%;
                                                margin-right: 8px;
                                                background-color: {{ $event['type'] === 'geofence_exit_event' ? '#EF4444' : '#10B981' }};">
                                            </span>
                                            {{ $event['type'] === 'geofence_exit_event' ? __('translations.geofence_exit_event', ['geofence' => $event['geofence']]) : __('translations.geofence_entry_event', ['geofence' => $event['geofence']]) }}
                                        </div>
                                    </td>
                                    <td>{{ $event['geofence'] }}</td>
                                    <td>{{ $event['time'] }}</td>
                                    <td>{{ $event['location'] }}</td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @endif

            <div class="details-section">
                <h3>Timeline</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Time</th>
                            <th>Type</th>
                            <th>Duration</th>
                            <th>Location</th>
                            <th>Distance</th>
                            <th>Fuel</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($data['details'] as $detail)
                            <tr style="background-color: {{ $detail['type'] == 'trip' ? '#e6f7ee' : '#f7e6e6' }}">
                                <td>{{ $detail['start_time'] }}-{{ $detail['end_time'] }}</td>
                                <td>
                                    <strong style="color: {{ $detail['type'] == 'trip' ? '#10B981' : '#EF4444' }}">
                                        {{ ucfirst($detail['type']) }}
                                    </strong>
                                </td>
                                <td>{{ $detail['duration'] }}</td>
                                <td>
                                    @if ($detail['type'] == 'stop')
                                        {{ $detail['location'] }}
                                    @else
                                        <div>From: {{ $detail['start_location'] }}</div>
                                        <div>To: {{ $detail['end_location'] }}</div>
                                    @endif
                                </td>
                                <td>
                                    @if ($detail['type'] == 'stop')
                                        {{-- {{ $detail['stop_distance'] ?? '-' }} --}}
                                        -
                                    @else
                                        {{ $detail['trip_distance'] ?? '-' }}
                                    @endif
                                </td>
                                <td>
                                    @if ($detail['type'] == 'stop')
                                        {{-- {{ $detail['stop_fuel'] ?? '-' }} --}}
                                        -
                                    @else
                                        {{ $detail['trip_fuel'] ?? '-' }}
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @elseif($reporting_type == 'alarms')
            <div class="details-section">
                <h3>Alarm Details</h3>
                <table>
                    <thead>
                        <tr>
                            <th>Vehicle</th>
                            <th>Alarm Type</th>
                            <th>Triggered At</th>
                            <th>Geofence</th>
                            <th>Location</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach ($data as $alarm)
                            <tr>
                                <td>{{ $alarm['Vehicle'] }}</td>
                                <td>{{ $alarm['Alarm Type'] }}</td>
                                <td>{{ $alarm['Triggered At'] }}</td>
                                <td>{{ $alarm['Geofence'] }}</td>
                                <td>{{ $alarm['Location'] }}</td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        @elseif($reporting_type == 'fuel_consumption')
            <div class="summary-box">
                <h3>Fuel Consumption Summary</h3>
                <div class="summary-grid">
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.vehicle')</div>
                        <div class="summary-value">{{ $data['summary']['vehicle'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.date')</div>
                        <div class="summary-value">{{ $data['summary']['date'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.total_distance')</div>
                        <div class="summary-value">{{ $data['summary']['total_distance'] }} km</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.total_fuel_used')</div>
                        <div class="summary-value">{{ $data['summary']['total_fuel_used'] }} L</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.average_consumption')</div>
                        <div class="summary-value">
                            @php
                                $avgConsumption =
                                    $data['summary']['total_distance'] > 0
                                        ? number_format(
                                            ($data['summary']['total_fuel_used'] * 100) /
                                                $data['summary']['total_distance'],
                                            2,
                                        )
                                        : 0;
                            @endphp
                            {{ $avgConsumption }} L/100km
                        </div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.total_trips')</div>
                        <div class="summary-value">{{ $data['summary']['total_trips'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.total_stops')</div>
                        <div class="summary-value">{{ $data['summary']['total_stops'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.travel_duration')</div>
                        <div class="summary-value">{{ $data['summary']['total_trip_duration'] }}</div>
                    </div>
                    <div class="summary-item">
                        <div class="summary-label">@lang('translations.stop_duration')</div>
                        <div class="summary-value">{{ $data['summary']['total_stop_duration'] }}</div>
                    </div>
                </div>
            </div>

            @if (!empty($data['details']))
                <div class="details-section">
                    <h3>Timeline</h3>
                    <table>
                        <thead>
                            <tr>
                                <th>Time</th>
                                <th>Type</th>
                                <th>Duration</th>
                                <th>Location</th>
                                <th>Distance</th>
                                <th>Fuel</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach ($data['details'] as $detail)
                                <tr style="background-color: {{ $detail['type'] == 'trip' ? '#e6f7ee' : '#f7e6e6' }}">
                                    <td>{{ $detail['start_time'] }}-{{ $detail['end_time'] }}</td>
                                    <td>
                                        <strong style="color: {{ $detail['type'] == 'trip' ? '#10B981' : '#EF4444' }}">
                                            {{ ucfirst($detail['type']) }}
                                        </strong>
                                    </td>
                                    <td>{{ $detail['duration'] }}</td>
                                    <td>
                                        @if ($detail['type'] == 'stop')
                                            {{ $detail['location'] }}
                                        @else
                                            <div>From: {{ $detail['start_location'] }}</div>
                                            <div>To: {{ $detail['end_location'] }}</div>
                                        @endif
                                    </td>
                                    <td>
                                        @if ($detail['type'] == 'stop')
                                            {{-- {{ $detail['trip_distance'] ?? '-' }} --}}
                                            -
                                        @else
                                            {{ $detail['trip_distance'] ?? '-' }}
                                        @endif
                                    </td>
                                    <td>
                                        @if ($detail['type'] == 'stop')
                                            {{-- {{ $detail['trip_fuel'] ?? '-' }} --}}
                                            -
                                        @else
                                            {{ $detail['trip_fuel'] ?? '-' }}
                                        @endif
                                    </td>
                                </tr>
                            @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <div class="no-data-message">
                    <p>No fuel consumption data available for this period.</p>
                </div>
            @endif
        @endif

        <div class="page-footer">
            Report generated on {{ date('d-m-Y H:i:s') }} | ControllOne © {{ date('Y') }}
        </div>
    </div>
</body>

</html>
